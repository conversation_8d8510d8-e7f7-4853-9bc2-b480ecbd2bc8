const SvgQDrant = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="168.419 120.023 131.984 152.407"
    width="1em"
    height="1em"
    {...props}
  >
    <defs>
      <linearGradient
        id="QDrant_svg__a"
        x1={62.128}
        x2={41.202}
        y1={105.54}
        y2={105.54}
        gradientTransform="translate(168.42 120.023)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0} stopColor="#FF3364" />
        <stop offset={1} stopColor="#C91540" stopOpacity={0} />
      </linearGradient>
    </defs>
    <g fillRule="evenodd" clipRule="evenodd">
      <path
        fill="#24386c"
        d="m272.21 260.113-3.038-83.784-5.504-22.089 36.735 3.889v101.35l-22.44 12.95z"
      />
      <path
        fill="#7589be"
        d="m300.4 158.123-22.44 12.96-46.308-10.158-54.203 22.069-9.03-24.871 32.99-19.05 33-19.05 32.99 19.05z"
      />
      <path
        fill="#b2bfe8"
        d="m168.42 158.123 22.44 12.96 13.008 38.686 43.921 35.142-13.378 27.512-33-19.051-32.99-19.05v-76.2"
      />
      <path
        fill="#24386c"
        d="m249.288 224.583-14.877 21.932v25.91l21.11-12.18 10.877-16.242"
      />
      <path
        fill="#7589be"
        d="m234.42 220.613-21.119-36.565 4.55-12.119 17.292-8.384 20.378 20.504z"
      />
      <path
        fill="#b2bfe8"
        d="m213.301 184.045 21.11 12.18v24.38l-19.524.84-11.81-15.08 10.224-22.32"
      />
      <path
        fill="#24386c"
        d="m234.411 196.223 21.11-12.179 14.367 23.922-17.386 14.365-18.09-1.727z"
      />
      <path
        fill="#dc244c"
        d="m255.521 260.243 22.44 12.181v-101.34l-21.78-12.57-21.77-12.57-21.78 12.57-21.77 12.57v50.289l21.77 12.57 21.78 12.571 21.11-12.191zm0-51.83-21.11 12.19-21.11-12.19v-24.37l21.11-12.19 21.11 12.19v24.37"
      />
    </g>
    <path
      fill="url(#QDrant_svg__a)"
      d="M234.421 246.523v-25.914l-21-12.086v25.871l21 12.129Z"
    />
  </svg>
);
export default SvgQDrant;
