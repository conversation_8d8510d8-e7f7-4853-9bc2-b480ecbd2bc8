const SvgCohere = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="165.883 110.821 117.8 117.78"
    width="1em"
    height="1em"
    {...props}
  >
    <g transform="translate(97.843 -127.708)">
      <clipPath id="cohere_svg__a">
        <path
          d="M68.05 238.52h117.78V356.3H68.05z"
          style={{
            overflow: "visible",
          }}
        />
      </clipPath>
      <g
        style={{
          clipPath: "url(#cohere_svg__a)",
        }}
      >
        <path
          d="M106.21 308.65c3.17 0 9.48-.17 18.19-3.76 10.16-4.18 30.37-11.77 44.94-19.57 10.2-5.45 14.66-12.67 14.66-22.38 0-13.48-10.93-24.41-24.41-24.41H103.1c-19.37 0-35.06 15.7-35.06 35.06s14.71 35.06 38.17 35.06z"
          className="cohere_svg__st2"
          style={{
            clipRule: "evenodd",
            fill: "#3a594d",
            fillRule: "evenodd",
          }}
        />
        <path
          d="M115.77 332.79c0-9.49 5.71-18.05 14.48-21.69l17.79-7.38c17.99-7.47 37.8 5.76 37.8 25.24 0 15.09-12.24 27.33-27.33 27.32h-19.26c-12.97-.01-23.48-10.52-23.48-23.49z"
          className="cohere_svg__st3"
          style={{
            clipRule: "evenodd",
            fill: "#bd8fc0",
            fillRule: "evenodd",
          }}
        />
        <path
          d="M88.27 313.27c-11.16 0-20.21 9.05-20.21 20.21v2.62c0 11.16 9.05 20.21 20.21 20.21s20.21-9.05 20.21-20.21v-2.62c0-11.16-9.05-20.21-20.21-20.21z"
          className="cohere_svg__st4"
          style={{
            fill: "#ee765c",
          }}
        />
      </g>
    </g>
  </svg>
);
export default SvgCohere;
