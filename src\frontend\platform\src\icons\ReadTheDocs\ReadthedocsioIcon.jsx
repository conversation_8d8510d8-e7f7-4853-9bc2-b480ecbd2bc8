const SvgReadthedocsioIcon = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" {...props}>
    <path
      fill="#32322a"
      d="M28.81 30.85a1.534 1.534 0 0 0-.208 3.014s3.736 1.25 10.097 1.763c5.108.417 10.9-.353 10.9-.353a1.534 1.534 0 1 0-.385-3.013s-5.666.705-10.276.32c-6.07-.48-9.385-1.603-9.385-1.603-.244-.06-.5-.06-.742 0zm0-7.6a1.535 1.535 0 0 0-.208 2.981s3.736 1.25 10.097 1.763c5.108.417 10.9-.353 10.9-.353.544-.07 1-.423 1.223-.928s.14-1.086-.193-1.523-.87-.663-1.416-.594c0 0-5.666.705-10.276.32-6.07-.48-9.385-1.603-9.385-1.603-.244-.06-.5-.06-.742 0zm0-7.604a1.534 1.534 0 0 0-.208 3.014s3.736 1.218 10.097 1.763c5.108.417 10.9-.353 10.9-.353.544-.07 1-.423 1.223-.928s.14-1.086-.193-1.523-.87-.663-1.416-.594c0 0-5.666.705-10.276.32-6.07-.48-9.385-1.603-9.385-1.603-.244-.06-.5-.06-.742 0zm0-7.604a1.534 1.534 0 0 0-.208 3.014s3.736 1.25 10.097 1.763c5.108.417 10.9-.353 10.9-.353a1.534 1.534 0 1 0-.385-3.013s-5.666.705-10.276.32c-6.07-.48-9.385-1.603-9.385-1.603-.244-.06-.5-.06-.742 0zM18.16.024c-8 0-10.966 2.5-10.966 2.5v59.667s2.907-2.5 12.265-2.116 11.288 3.664 22.79 3.895c11.5.32 14.392-1.763 14.392-1.763l.167-60.828S51.63 2.855 41.558 2.92C31.486 2.92 29.065.354 19.82.034a42.3 42.3 0 0 0-1.657-.029zm6.685 3.895s4.84 1.603 13.784 2.052c7.558.385 15.137-.737 15.137-.737v54.06s-3.836 2.02-13.425 1.314c-7.43-.545-15.607-3.344-15.607-3.344zm-4.668 1.4a1.554 1.554 0 1 1 0 3.11s-2.504.013-4.033.32c-2.567.32-4.31 1.186-4.31 1.186a1.542 1.542 0 0 1-2.36-1.405 1.546 1.546 0 0 1 .926-1.318s2.273-1.186 5.442-1.507c1.83-.32 4.337-.32 4.337-.32zm-1.492 7.623a26.95 26.95 0 0 1 1.492 0 1.546 1.546 0 0 1 0 3.08s-2.504.013-4.033.32c-2.567.32-4.31 1.186-4.31 1.186a1.544 1.544 0 0 1-1.436-2.726s2.273-1.218 5.442-1.507c.916 0 2 0 2.845-.32zm1.492 7.597a1.554 1.554 0 1 1 0 3.11s-2.504-.016-4.033 0c-2.567.32-4.31 1.186-4.31 1.186a1.544 1.544 0 0 1-1.436-2.726s2.273-1.186 5.442-1.507c1.83-.32 4.337-.32 4.337-.32z"
    />
  </svg>
);
export default SvgReadthedocsioIcon;
