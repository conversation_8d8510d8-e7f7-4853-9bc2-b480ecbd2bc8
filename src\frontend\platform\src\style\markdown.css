.bs-mkdown h1 {
    font-size: 24px;
}

.bs-mkdown h2 {
    margin: 0;
    font-size: 20px;
}

.bs-mkdown h3 {
    margin: 0;
    font-size: 16px;
}

.bs-mkdown h4 {
    margin: 0;
    font-size: 16px;
}

.bs-mkdown h5 {
    font-size: 16px;
}

.bs-mkdown h6 {
    font-size: 14px;
}

.bs-mkdown ol,
.bs-mkdown ul {
    white-space: normal !important;
}

.bs-mkdown {
    color: var(--tw-prose-body);
    max-width: 65ch;
}

:root .bs-mkdown {
    --mk-bc: 215 28% 17%;
    --tw-prose-body: hsl(var(--mk-bc) / 0.8);
    --tw-prose-headings: hsl(var(--mk-bc));
    --tw-prose-lead: hsl(var(--mk-bc));
    --tw-prose-links: hsl(var(--mk-bc));
    --tw-prose-bold: hsl(var(--mk-bc));
    --tw-prose-counters: hsl(var(--mk-bc));
    --tw-prose-bullets: hsl(var(--mk-bc) / 0.5);
    --tw-prose-hr: hsl(var(--mk-bc) / 0.2);
    --tw-prose-quotes: hsl(var(--mk-bc));
    --tw-prose-quote-borders: hsl(var(--mk-bc) / 0.2);
    --tw-prose-captions: hsl(var(--mk-bc) / 0.5);
    --tw-prose-code: hsl(var(--mk-bc));
    --tw-prose-pre-code: hsl(var(--nc));
    --tw-prose-pre-bg: hsl(var(--n));
    --tw-prose-th-borders: hsl(var(--mk-bc) / 0.5);
    --tw-prose-td-borders: hsl(var(--mk-bc) / 0.2);
}

.dark .bs-mkdown {
    --mk-bc: 0 0% 100%;
}

.bs-mkdown :where(code):not(:where([class~="not-prose"] *)) {
    padding: 2px 8px;
    border-radius: var(--rounded-badge);
}

.bs-mkdown code:after,
.bs-mkdown code:before {
    content: none;
}

.bs-mkdown pre code {
    border-radius: 0;
    padding: 0;
}

.bs-mkdown :where(tbody tr, thead):not(:where([class~="not-prose"] *)) {
    border-bottom-color: hsl(var(--mk-bc) / 20%);
}

.bs-mkdown :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.4em;
    margin-bottom: 0.4em;
}

.bs-mkdown :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-lead);
    font-size: 1.25em;
    line-height: 1.6;
    margin-top: 1.2em;
    margin-bottom: 1.2em;
}

.bs-mkdown :where(a):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-links);
    text-decoration: underline;
    font-weight: 500;
}

.bs-mkdown :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-bold);
    font-weight: 600;
}

/* 暗黑模式下<strong>标签字体颜色 */
.dark .bs-mkdown :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: white;
}

.bs-mkdown :where(a strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(blockquote strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(thead th strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: decimal;
    margin-top: .4em;
    margin-bottom: .4em;
    padding-left: 1.625em;
}

.bs-mkdown :where(ol[type="A"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
}

.bs-mkdown :where(ol[type="a"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
}

.bs-mkdown :where(ol[type="A"s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
}

.bs-mkdown :where(ol[type="a"s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
}

.bs-mkdown :where(ol[type="I"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
}

.bs-mkdown :where(ol[type="i"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
}

.bs-mkdown :where(ol[type="I"s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
}

.bs-mkdown :where(ol[type="i"s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
}

.bs-mkdown :where(ol[type="1"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: decimal;
}

.bs-mkdown :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: disc;
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 1.625em;
}

.bs-mkdown :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    font-weight: 400;
    color: var(--tw-prose-counters);
}

.bs-mkdown :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-bullets);
}

.bs-mkdown :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    margin-top: 1.25em;
}

.bs-mkdown :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
}

.bs-mkdown :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 500;
    font-style: italic;
    color: var(--tw-prose-quotes);
    border-left-width: 0.25rem;
    border-left-color: var(--tw-prose-quote-borders);
    quotes: "\201C""\201D""\2018""\2019";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-left: 1em;
}

.bs-mkdown :where(blockquote p:first-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *))::before {
    content: open-quote;
}

.bs-mkdown :where(blockquote p:last-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
    content: close-quote;
}

.bs-mkdown :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 800;
    font-size: 2.25em;
    margin-top: 0;
    margin-bottom: 0.8888889em;
    line-height: 1.1111111;
}

.bs-mkdown :where(h1 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 900;
    color: inherit;
}

.bs-mkdown :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 700;
    font-size: 1.5em;
    margin-top: 2em;
    margin-bottom: 1em;
    line-height: 1.3333333;
}

.bs-mkdown :where(h2 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 800;
    color: inherit;
}

.bs-mkdown :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    font-size: 1.25em;
    margin-top: 1.6em;
    margin-bottom: 0.6em;
    line-height: 1.6;
}

.bs-mkdown :where(h3 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 700;
    color: inherit;
}

.bs-mkdown :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: 1.5;
}

.bs-mkdown :where(h4 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 700;
    color: inherit;
}

.bs-mkdown :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
}

.bs-mkdown :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    display: block;
    margin-top: 2em;
    margin-bottom: 2em;
}

.bs-mkdown :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: 500;
    font-family: inherit;
    color: var(--tw-prose-kbd);
    box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
    font-size: 0.875em;
    border-radius: 0.3125rem;
    padding-top: 0.1875em;
    padding-right: 0.375em;
    padding-bottom: 0.1875em;
    padding-left: 0.375em;
}

.bs-mkdown :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-code);
    font-weight: 600;
    font-size: 0.875em;
}

.bs-mkdown :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::before {
    content: "`";
}

.bs-mkdown :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
    content: "`";
}

.bs-mkdown :where(a code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(h1 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: 0.875em;
}

.bs-mkdown :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: 0.9em;
}

.bs-mkdown :where(h4 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(blockquote code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(thead th code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
}

.bs-mkdown :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-pre-code);
    background-color: var(--tw-prose-pre-bg);
    overflow-x: auto;
    font-weight: 400;
    font-size: 0.875em;
    line-height: 1.7142857;
    margin-top: 1.7142857em;
    margin-bottom: 1.7142857em;
    border-radius: 0.375rem;
    padding-top: 0.8571429em;
    padding-right: 1.1428571em;
    padding-bottom: 0.8571429em;
    padding-left: 1.1428571em;
}

.bs-mkdown :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    background-color: transparent;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-weight: inherit;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
}

.bs-mkdown :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *))::before {
    content: none;
}

.bs-mkdown :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *))::after {
    content: none;
}

.bs-mkdown :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    width: 100%;
    table-layout: auto;
    text-align: left;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: 0.875em;
    line-height: 1.7142857;
}

.bs-mkdown :where(thead):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-th-borders);
}

.bs-mkdown :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    font-weight: 600;
    vertical-align: bottom;
    padding-right: 0.5714286em;
    padding-bottom: 0.5714286em;
    padding-left: 0.5714286em;
}

.bs-mkdown :where(tbody tr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-td-borders);
}

.bs-mkdown :where(tbody tr:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 0;
}

.bs-mkdown :where(tbody td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: baseline;
}

.bs-mkdown :where(tfoot):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-top-width: 1px;
    border-top-color: var(--tw-prose-th-borders);
}

.bs-mkdown :where(tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: top;
}

.bs-mkdown :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
}

.bs-mkdown :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-captions);
    font-size: 0.875em;
    line-height: 1.4285714;
    margin-top: 0.8571429em;
}

.bs-mkdown {
    --tw-prose-body: #374151;
    --tw-prose-headings: #111827;
    --tw-prose-lead: #4b5563;
    --tw-prose-links: #111827;
    --tw-prose-bold: #111827;
    --tw-prose-counters: #6b7280;
    --tw-prose-bullets: #d1d5db;
    --tw-prose-hr: #e5e7eb;
    --tw-prose-quotes: #111827;
    --tw-prose-quote-borders: #e5e7eb;
    --tw-prose-captions: #6b7280;
    --tw-prose-kbd: #111827;
    --tw-prose-kbd-shadows: 17 24 39;
    --tw-prose-code: #111827;
    --tw-prose-pre-code: #e5e7eb;
    --tw-prose-pre-bg: #1f2937;
    --tw-prose-th-borders: #d1d5db;
    --tw-prose-td-borders: #e5e7eb;
    --tw-prose-invert-body: #d1d5db;
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: #9ca3af;
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: #9ca3af;
    --tw-prose-invert-bullets: #4b5563;
    --tw-prose-invert-hr: #374151;
    --tw-prose-invert-quotes: #f3f4f6;
    --tw-prose-invert-quote-borders: #374151;
    --tw-prose-invert-captions: #9ca3af;
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: #d1d5db;
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: #4b5563;
    --tw-prose-invert-td-borders: #374151;
    font-size: 1rem;
    line-height: 1.75;
}

.bs-mkdown :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
}

.bs-mkdown :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
}

.bs-mkdown :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.2em;
    margin-bottom: 0.2em;
}

.bs-mkdown :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0.375em;
}

.bs-mkdown :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0.375em;
}

.bs-mkdown :where(.bs-mkdown > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
}

.bs-mkdown :where(.bs-mkdown > ul > li > *:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
}

.bs-mkdown :where(.bs-mkdown > ul > li > *:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
}

.bs-mkdown :where(.bs-mkdown > ol > li > *:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
}

.bs-mkdown :where(.bs-mkdown > ol > li > *:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
}

.bs-mkdown :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
}

.bs-mkdown :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
}

.bs-mkdown :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0.5em;
    padding-left: 1.625em;
}

.bs-mkdown :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
}

.bs-mkdown :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
}

.bs-mkdown :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
}

.bs-mkdown :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
}

.bs-mkdown :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0;
}

.bs-mkdown :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-right: 0;
}

.bs-mkdown :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: 0.5714286em;
    padding-right: 0.5714286em;
    padding-bottom: 0.5714286em;
    padding-left: 0.5714286em;
}

.bs-mkdown :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-left: 0;
}

.bs-mkdown :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-right: 0;
}

.bs-mkdown :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
}

.bs-mkdown :where(.bs-mkdown > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
}

.bs-mkdown :where(.bs-mkdown > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
}