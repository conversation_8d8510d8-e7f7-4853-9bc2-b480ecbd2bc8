const SvgShare = (props) => (
  <svg width="1em" height="1em" {...props} xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.995 19.5a7.232 7.232 0 0 0 2.898-.585 7.582 7.582 0 0 0 2.392-1.627 7.748 7.748 0 0 0 1.625-2.398c.393-.904.59-1.868.59-2.89a7.172 7.172 0 0 0-.59-2.89 7.708 7.708 0 0 0-4.026-4.024 7.231 7.231 0 0 0-2.898-.586 7.209 7.209 0 0 0-2.888.586c-.905.39-1.7.932-2.387 1.626A7.788 7.788 0 0 0 5.09 9.11 7.172 7.172 0 0 0 4.5 12c0 1.022.197 1.986.59 2.89a7.748 7.748 0 0 0 1.625 2.398 7.582 7.582 0 0 0 2.392 1.627c.904.39 1.867.585 2.888.585Zm-3.687-4.238a.423.423 0 0 1-.316-.149c-.093-.099-.14-.247-.14-.446 0-.966.14-1.812.419-2.537.279-.725.721-1.29 1.328-1.696.607-.406 1.406-.609 2.396-.609h.093V8.366c0-.142.051-.266.154-.372a.514.514 0 0 1 .385-.158c.105 0 .198.024.279.07.08.047.182.129.306.246l3.167 2.956c.068.068.118.14.149.214.03.074.046.148.046.223a.578.578 0 0 1-.046.223.668.668 0 0 1-.149.213l-3.167 2.984c-.21.198-.409.297-.594.297a.52.52 0 0 1-.53-.511v-1.487h-.093c-.736 0-1.354.115-1.853.344-.498.229-.93.675-1.295 1.338-.075.136-.16.223-.256.26a.78.78 0 0 1-.283.056Z"
      fill="currentColor"
    ></path>
  </svg>
);
export default SvgShare;
