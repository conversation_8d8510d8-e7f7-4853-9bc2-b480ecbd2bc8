const SvgChroma = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 209 135"
    {...props}
  >
    <ellipse cx={136.019} cy={67.23} fill="#FFDE2D" rx={66.667} ry={64} />
    <ellipse cx={69.352} cy={67.23} fill="#327EFF" rx={66.667} ry={64} />
    <path
      fill="#327EFF"
      d="M2.685 67.23c0-35.346 29.848-64 66.667-64v64H2.685Z"
    />
    <path
      fill="#FF6446"
      d="M136.019 67.23c0 35.347-29.848 64-66.667 64v-64h66.667ZM69.352 67.23c0-35.346 29.848-64 66.667-64v64H69.352Z"
    />
  </svg>
);
export default SvgChroma;
