const SvgShare2 = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    x="0px"
    y="0px"
    width="1em"
    height="1em"
    viewBox="0 0 485.213 485.212"
    fill="currentColor"
    {...props}
  >
    <g>
      <path d="M394.236,212.282L272.934,333.584V272.93c0,0-121.304-30.324-181.955,60.654c0-100.483,81.469-181.956,181.955-181.956   V90.978L394.236,212.282z M485.212,242.606c0,133.976-108.627,242.606-242.604,242.606c-133.994,0-242.606-108.631-242.606-242.606   C0.001,108.628,108.613,0,242.607,0C376.585,0,485.212,108.628,485.212,242.606z M454.89,242.606   c0-117.038-95.241-212.279-212.282-212.279c-117.055,0-212.28,95.241-212.28,212.279c0,117.039,95.225,212.28,212.28,212.28   C359.648,454.886,454.89,359.645,454.89,242.606z" />
    </g>
  </svg>
);
export default SvgShare2;
