@echo off
echo Starting BISHENG Celery Worker...
echo Working directory: %cd%
echo Target directory: src\backend

if not exist "src\backend" (
    echo Error: src\backend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

cd src\backend

echo Current directory: %cd%
echo Running: poetry run celery -A bisheng.worker.main worker -l info --pool=solo
echo Press Ctrl+C to stop the worker
echo ----------------------------------------

poetry run celery -A bisheng.worker.main worker -l info --pool=solo

pause
