@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  #root {
    @apply font-sans;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}


@layer components {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
  }
  .template-scrollbar {
    @apply overflow-y-auto max-h-[51vh] no-scrollbar
  }
  .round-buttons-position {
    @apply fixed right-4
  }
  .side-bar-arrangement {
    @apply flex h-full w-52 flex-col overflow-hidden scrollbar-hide;
    box-shadow: 1px 0 2px #eee; /* me */
  }
  .side-bar-search-div-placement {
    @apply relative mx-auto mb-2 mt-2 flex items-center
  }
  .side-bar-components-icon {
    @apply h-6 w-4 text-ring
  }
  .side-bar-components-text {
    @apply w-full truncate pr-1 text-xs text-foreground
  }
  .side-bar-components-div-form {
    @apply flex w-full items-center justify-between rounded-md rounded-l-none border border-l-0 border-dashed border-ring  bg-white px-3 py-1 text-sm
  }
  .side-bar-components-border {
    @apply cursor-grab rounded-l-md border-l-8
  }
  .side-bar-components-gap {
    @apply flex flex-col gap-2 p-2
  }
  .side-bar-components-div-arrangement {
    @apply w-full overflow-auto scrollbar-hide
  }
  .search-icon {
    @apply absolute inset-y-0 right-0 flex items-center py-1.5 pr-5
  }
  .extra-side-bar-save-disable {
    @apply text-muted-foreground
  }
  .extra-side-bar-save-disable:hover {
    @apply hover:text-accent-foreground
  }
  .side-bar-button-size {
    @apply h-5 w-5
  }
  .side-bar-button-size:hover {
    @apply hover:text-accent-foreground
  }
  .side-bar-buttons-arrangement {
    @apply mb-2 mt-2 flex w-full items-center justify-between gap-2 px-2
  }
  .extra-side-bar-buttons {
    @apply relative inline-flex w-full items-center justify-center rounded-full bg-background px-2 py-2 text-foreground shadow-sm ring-1 ring-inset ring-input transition-all duration-500 ease-in-out
  }
  .extra-side-bar-buttons:hover {
    @apply hover:bg-muted
  }
  .button-div-style {
    @apply gap-2 flex
  }
  .input-primary:focus{
    @apply focus:placeholder-transparent focus:ring-ring focus:border-ring
  }
  .input-primary {
    @apply bg-background block border-border form-input px-3 placeholder:text-muted-foreground rounded-md shadow-sm sm:text-sm truncate w-full;
  }

  .input-edit-node{
    @apply input-primary border-border placeholder:text-center pt-0.5 pb-0.5 text-center
  }
  .input-search{
    @apply input-primary pr-7 mx-2
  }
  .input-disable{
    @apply bg-border placeholder:text-ring border-transparent
  }
  .input-dialog{
    @apply text-ring cursor-pointer bg-transparent
  }
  .message-button {
    @apply message-button-position flex h-12 w-12 items-center justify-center rounded-full bg-border px-3 py-1 shadow-md transition-all
  }
  
  .round-button-form {
    @apply flex h-12 w-12 cursor-pointer justify-center rounded-full bg-border px-3 py-1 shadow-md
  }
  .round-button-div {
    @apply flex items-center gap-3
  }
  .build-trigger-loading-icon {
    @apply stroke-build-trigger
  }
  .build-trigger-icon {
    @apply w-6 fill-build-trigger stroke-1 stroke-build-trigger
  }
  .message-button-position {
    @apply fixed bottom-4 right-4
  }
  .message-button-icon {
    @apply fill-chat-trigger stroke-chat-trigger stroke-1
  }
  .disabled-message-button-icon {
    @apply fill-chat-trigger-disabled stroke-chat-trigger-disabled stroke-1
  }
  .components-disclosure-arrangement {
    @apply -mt-px flex w-full select-none items-center justify-between px-3 py-2 bg-white dark:bg-gray-950;
    border: none;
    box-shadow: 0 1px 10px 0 #eee;
    margin-bottom: 2px;
    border-radius: 0 4px 22px 0;
  }
  .components-disclosure-arrangement:hover {
    @apply bg-muted transition-colors
  }
  .components-disclosure-title {
    @apply flex items-center text-xs break-keep
  }
  .components-disclosure-div {
    @apply flex gap-2
  }
  .flow-page-positioning {
    @apply h-full w-full overflow-hidden
  }
  .logspace-page-icon {
    @apply absolute bottom-2 left-7 flex h-6 cursor-pointer flex-col items-center justify-start overflow-hidden rounded-lg bg-foreground px-2 text-center font-sans text-xs tracking-wide text-secondary transition-all duration-500 ease-in-out
  }

  .logspace-page-icon:hover {
    @apply hover:h-12
  }
  
  .flex-max-width {
    @apply flex w-full
  }

  .main-page-panel {
    @apply flex-max-width h-full flex-col overflow-auto bg-muted px-16
  }

  .main-page-nav-arrangement {
    @apply flex-max-width justify-between px-6 py-12 pb-2
  }

  .main-page-nav-title {
    @apply flex items-center justify-center gap-2 text-2xl font-semibold
  }

  .main-page-nav-button {
    @apply mr-2 w-4
  }

  .main-page-description-text {
    @apply flex w-[60%] px-6 pb-14 text-muted-foreground
  }

  .main-page-flows-display {
    @apply grid w-full gap-4 p-4 md:grid-cols-2 lg:grid-cols-4
  }

  .community-page-arrangement {
    @apply flex-max-width h-full flex-col overflow-auto bg-muted px-16
  }

  .community-page-nav-arrangement {
    @apply flex-max-width justify-between px-6 py-12 pb-2
  }

  .community-page-nav-title {
    @apply flex items-center justify-center gap-2 text-2xl font-semibold
  }

  .community-page-nav-button {
    @apply flex gap-2
  }

  .community-page-description-text {
    @apply flex w-[70%] px-6 pb-8 text-muted-foreground 
  }

  .community-pages-flows-panel {
    @apply grid w-full gap-4 p-4 md:grid-cols-2 lg:grid-cols-4
  }
  .generic-node-div {
    @apply relative flex w-96 flex-col justify-center rounded-3xl bg-background
  }
  .generic-node-div-title {
    @apply flex w-full items-center justify-between gap-8 rounded-t-3xl border-b bg-muted p-4
  }
  .generic-node-title-arrangement {
    @apply flex-max-width items-center truncate
  }
  .generic-node-icon {
    @apply h-10 w-10 rounded p-1
  }
  .generic-node-tooltip-div {
    @apply ml-2 truncate
  }
  .generic-node-validation-div {
    @apply max-h-96 overflow-auto
  }

  .generic-node-status-position {
    @apply relative top-[3px] h-5 w-5
  }

  .generic-node-status-animation {
    @apply hidden h-4 w-4 animate-spin rounded-full bg-ring opacity-0
  }
  .generic-node-status {
    @apply h-4 w-4 rounded-full opacity-100
  }
  .green-status {
    @apply generic-node-status bg-status-green
  }
  .red-status {
    @apply generic-node-status bg-status-red
  }
  .yellow-status {
    @apply generic-node-status bg-status-yellow
  }
  .status-build-animation {
    @apply hidden h-4 w-4 animate-spin rounded-full bg-ring opacity-0
  }
  .status-div {
    @apply absolute w-4 duration-200 ease-in-out
  }
  .status-div:hover {
    @apply hover:text-accent-foreground hover:transition-all
  }
  .generic-node-desc {
    @apply h-full w-full py-5 text-foreground
  }
  .generic-node-desc-text {
    @apply w-full px-5 pb-3 text-sm text-muted-foreground
  }

  .alert-icon {
    @apply h-5 w-5
  }
  .alert-font-size {
    @apply text-sm font-medium
  }

  .error-build-message {
    @apply mt-6 w-96 cursor-pointer rounded-md bg-error-background  p-4 shadow-xl
  }
  .error-build-message-circle {
    @apply text-status-red alert-icon
  }
  .error-build-text {
    @apply text-error-foreground
  }
  .error-build-foreground {
    @apply error-build-text alert-font-size
  }
  .error-build-message-div {
    @apply mt-2 text-sm error-build-text
  }
  .error-build-message-list {
    @apply list-disc space-y-1 pl-5
  }

  .success-alert {
    @apply mt-6 w-96 rounded-md bg-success-background p-4 shadow-xl
  }
  .success-alert-icon {
    @apply alert-icon text-status-green
  }
  .success-alert-message {
    @apply alert-font-size text-success-foreground
  }

  .card-component-title-display {
    @apply round-button-div flex-max-width
  }
  .card-component-image {
    @apply flex h-7 w-7 items-center justify-center rounded-full text-2xl
  }
  .card-component-title-size {
    @apply inline-block w-full flex-1 break-words truncate-doubleline
  }
  .card-component-delete-button {
    @apply flex self-start
  }
  .card-component-delete-icon {
    @apply h-4 w-4 text-primary opacity-0 transition-all group-hover:opacity-100
  }
  .card-component-desc {
    @apply pb-2 pt-2
  }
  .card-component-desc-text {
    @apply truncate-doubleline
  }
  .card-component-footer-arrangement {
    @apply flex-max-width items-end justify-between gap-2
  }
  .card-component-footer {
    @apply flex flex-wrap gap-2
  }

  .unused-side-bar-aside {
    @apply flex flex-shrink-0 flex-col overflow-hidden border-r  transition-all duration-500
  }
  .unused-side-bar-arrangement {
    @apply flex h-full  w-52 flex-col items-start overflow-y-auto border bg-background scrollbar-hide
  }
  .unused-side-bar-division {
    @apply flex-max-width flex-grow flex-col
  }
  .unused-side-bar-nav {
    @apply flex-1 space-y-1
  }
  .unused-side-bar-link {
    @apply flex-max-width items-center rounded-md py-2 pl-2 text-sm font-medium
  }
  .unused-side-bar-link-colors-true {
    @apply bg-muted text-foreground
  }
  .unused-side-bar-link-colors-false {
    @apply bg-background text-muted-foreground hover:bg-muted hover:text-foreground
  }
  .unused-side-bar-icon {
    @apply mr-3 flex-shrink-0 h-6 w-6
  }
  .unused-side-bar-icon-false {
    @apply text-ring group-hover:text-accent-foreground
  }
  .unused-side-bar-disclosure {
    @apply unused-side-bar-link pr-1 text-left
  }
  .unused-side-bar-disclosure:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-ring
  }
  .unused-side-bar-disclosure-icon {
    @apply unused-side-bar-icon text-ring group-hover:text-accent-foreground
  }
  .unused-side-bar-svg-true {
    @apply text-ring rotate-90
  }
  .unused-side-bar-svg {
    @apply ml-3 h-5 w-5 flex-shrink-0 duration-150 ease-in-out group-hover:text-accent-foreground
  }
  .unused-side-bar-disclosure-panel {
    @apply flex w-full items-center rounded-md py-2 pl-11 pr-2 text-sm font-medium
  }

  .code-area-component {
    @apply pointer-events-none w-full cursor-not-allowed
  }
  .code-area-input-positioning {
    @apply flex-max-width items-center
  }
  .code-area-external-link {
    @apply w-6 h-6 ml-3
  }
  .code-area-external-link:hover {
    @apply hover:text-accent-foreground
  }

  .dropdown-component-outline {
    @apply  input-edit-node relative pr-8
  }
  .dropdown-component-false-outline {
    @apply input-primary py-2 pl-3 pr-10 text-left
  }
  .dropdown-component-display {
    @apply block w-full truncate bg-background
  }
  .dropdown-component-arrow {
    @apply pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2
  }
  .dropdown-component-arrow-color {
    @apply h-5 w-5 extra-side-bar-save-disable
  }
  .dropdown-component-options {
    @apply z-10 mt-1 max-h-60 overflow-auto rounded-md bg-background py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm
  }
  .dropdown-component-true-options {
    @apply dropdown-component-options w-[215px]
  }
  .dropdown-component-false-options {
    @apply dropdown-component-options w-full
  }
  .dropdown-component-option {
    @apply relative cursor-default select-none
  }
  .dropdown-component-false-option {
    @apply dropdown-component-option py-0.5 pl-3 pr-12
  }
  .dropdown-component-true-option {
    @apply dropdown-component-option py-2 pl-3 pr-9
  }
  .dropdown-component-choosal {
    @apply absolute inset-y-0 right-0 flex items-center pr-4
  }
  .dropdown-component-check-icon {
    @apply h-5 w-5 text-black
  }

  .edit-flow-arrangement {
    @apply flex justify-between
  }
  .edit-flow-span {
    @apply ml-10 animate-pulse text-status-red
  }

  .float-component-pointer {
    @apply pointer-events-none cursor-not-allowed
  }

  .header-menu-bar {
    @apply flex items-center gap-0.5 rounded-md px-1.5 py-1 text-sm font-medium
  }
  .header-menu-bar-display {
    @apply flex max-w-[200px] items-center gap-2 cursor-pointer
  }
  .header-menu-flow-name {
    @apply flex-1 truncate
  }
  .header-menu-options {
    @apply mr-2 h-4 w-4
  }

  .header-arrangement {
    @apply flex-max-width h-12 items-center justify-between border-b bg-muted
  }
  .header-start-display {
    @apply flex w-96 items-center justify-start gap-2
  }
  .header-end-division {
    @apply flex w-96 justify-end px-2
  }
  .header-end-display {
    @apply ml-auto mr-2 flex items-center gap-5
  }
  .header-github-link-box {
    @apply border border-input h-9 px-3 pr-0 rounded-md inline-flex shadow-sm items-center justify-center
  }
  .header-github-link {
    @apply text-sm font-medium  disabled:opacity-50 disabled:pointer-events-none ring-offset-background text-muted-foreground header-github-link-box
  }
  .header-github-link:focus-visible {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
  }
  .header-github-link:hover {
    @apply hover:bg-accent hover:text-accent-foreground
  }
  .header-github-display {
    @apply -mr-px ml-2 flex h-9 items-center justify-center rounded-md rounded-l-none border bg-background px-2 text-sm
  }
  .header-notifications-box {
    @apply fixed left-0 top-0 h-screen w-screen
  }
  .header-notifications {
    @apply absolute right-3 top-2 h-1.5 w-1.5 rounded-full bg-destructive
  }

  .input-component-div {
    @apply pointer-events-none relative cursor-not-allowed
  }
  .input-component-button {
    @apply absolute inset-y-0 right-0 items-center text-muted-foreground
  }
  .input-component-true-button {
    @apply input-component-button pr-2
  }
  .input-component-false-button {
    @apply input-component-button px-4
  }
  .input-component-true-svg {
    @apply absolute bottom-0.5 right-2 side-bar-button-size
  }
  .input-component-false-svg {
    @apply absolute bottom-2 right-3 side-bar-button-size
  }
  
  .input-file-component {
    @apply flex-max-width items-center
  }

  .toggle-component-switch {
    @apply relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out
  }
  .toggle-component-switch:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-primary focus:ring-offset-1
  }
  .toggle-component-span {
    @apply pointer-events-none relative inline-block h-5 w-5 transform rounded-full  shadow ring-0 transition duration-200 ease-in-out
  }
  .toggle-component-second-span {
    @apply absolute inset-0 flex h-full w-full items-center justify-center transition-opacity
  }

  .app-div {
    @apply fixed bottom-5 left-5 flex flex-col-reverse
  }

  .chat-input-modal-txtarea {
    @apply form-input block w-full rounded-md border-ring pr-10  custom-scroll sm:text-sm
  }
  .chat-input-modal-div {
    @apply absolute bottom-0.5 right-3
  }
  .chat-input-modal-lock {
    @apply side-bar-button-size animate-pulse text-ring
  }
  .chat-input-modal-send {
    @apply side-bar-button-size text-ring hover:text-muted-foreground
  }

  .code-block-modal {
    @apply flex items-center justify-between px-4 py-1.5
  }
  .code-block-modal-span {
    @apply text-xs lowercase text-muted-foreground
  }
  .code-block-modal-button {
    @apply flex items-center gap-1.5 rounded bg-none p-1 text-xs text-muted-foreground
  }

  .chat-message-modal {
    @apply flex-max-width py-2 pl-2
  }
  .chat-message-modal-div {
    @apply my-3 flex h-8 w-8 items-center justify-center overflow-hidden rounded-full
  }
  .chat-message-modal-img {
    @apply absolute scale-150 transition-opacity duration-500
  }
  .chat-message-modal-display {
    @apply flex-max-width items-center text-start
  }
  .chat-message-modal-text {
    @apply relative inline-block w-full text-start text-sm  font-normal text-muted-foreground
  }
  .chat-message-modal-icon-div {
    @apply absolute -left-2 -top-1 cursor-pointer
  }
  .chat-message-modal-thought {
    @apply ml-3 inline-block h-full w-[95%] rounded-md border border-ring bg-muted px-2 pb-3 pt-3 text-start text-muted-foreground chat-message-modal-thought-cursor
  }
  .chat-message-modal-thought-cursor {
    @apply cursor-pointer scrollbar-hide overflow-scroll
  }
  .chat-message-modal-markdown {
    @apply w-full px-4 pb-3 pr-8 pt-3
  }
  .chat-message-modal-markdown-span {
    @apply mt-1 animate-pulse cursor-default
  }
  .chat-message-modal-alert {
    @apply inline-block px-3 text-start text-muted-foreground
  }

  .file-card-modal-image-div {
    @apply absolute right-0 top-0 rounded-bl-lg bg-muted px-1 text-sm font-bold text-foreground
  }
  .file-card-modal-image-button {
    @apply px-2 py-1 text-ring
  }
  .file-card-modal-button {
    @apply flex w-1/2 items-center justify-between rounded border border-ring bg-muted px-2 py-2 text-foreground shadow hover:drop-shadow-lg
  }
  .file-card-modal-div {
    @apply mr-2 flex-max-width items-center gap-2 text-current
  }
  .file-card-modal-footer {
    @apply flex flex-col items-start
  }
  .file-card-modal-name {
    @apply truncate text-sm text-current
  }
  .file-card-modal-type {
    @apply truncate text-xs text-ring
  }

  .send-message-modal-transition {
    @apply fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm transition-opacity
  }
  .chat-modal-box {
    @apply fixed inset-0 z-10 overflow-y-auto
  }
  .chat-modal-box-div {
    @apply flex h-full items-end justify-center p-4 text-center sm:items-center sm:p-0
  }
  .chat-modal-dialog-panel {
    @apply relative flex h-[95%] w-[690px] transform flex-col justify-between overflow-hidden rounded-lg bg-background text-left shadow-xl drop-shadow-2xl transition-all
  }
  .chat-modal-dialog-panel-div {
    @apply relative w-full p-4
  }
  .chat-modal-dialog-trash-panel {
    @apply absolute right-10 top-2 z-30 text-muted-foreground hover:text-status-red
  }
  .chat-modal-dialog-x-panel {
    @apply absolute right-2 top-1.5 z-30 text-muted-foreground hover:text-status-red
  }
  .chat-modal-dialog-history {
    @apply flex-max-width h-full flex-col items-center overflow-scroll border-t bg-background scrollbar-hide
  }
  .chat-modal-dialog-span-box {
    @apply flex-max-width h-full flex-col items-center justify-center text-center align-middle
  }
  .chat-modal-dialog-desc {
    @apply w-2/4 rounded-md border border-input bg-muted px-6 py-8
  }
  .chat-modal-input-div {
    @apply flex-max-width flex-col items-center justify-between border-t bg-background p-3
  }
  .chat-modal-input {
    @apply relative mt-1 w-full rounded-md shadow-sm
  }
  .code-area-modal-editor-div {
    @apply mt-2 flex-max-width h-full
  }
  .code-area-modal-editor-box {
    @apply h-[300px] w-full rounded-lg border-[1px] border-ring custom-scroll
  }

  .edit-node-modal-variable {
    @apply h-5 w-5 stroke-2 pe-1 text-muted-foreground
  }
  .edit-node-modal-span {
    @apply text-sm font-semibold text-primary
  }
  .edit-node-modal-arrangement {
    @apply flex-max-width h-fit max-h-[400px]
  }
  .edit-node-modal-box {
    @apply w-full rounded-lg border-[1px] border-input bg-background
  }
  .edit-node-modal-table {
    @apply flex h-fit flex-col gap-5
  }
  .edit-node-modal-table-header {
    @apply h-10 border-input text-xs font-medium text-ring
  }
  .edit-node-modal-table-cell {
    @apply p-0 text-center text-sm text-foreground truncate sm:px-3
  }
  .edit-node-modal-second-cell {
    @apply w-[300px] p-0 text-center text-xs text-foreground
  }

  .generic-modal-txtarea-div {
    @apply mt-2 flex-max-width h-full
  }

  .button-box-modal-div {
    @apply flex transform flex-col items-center justify-center rounded-lg border border-ring text-center shadow hover:scale-105 hover:shadow-lg
  }

  .dialog-header-modal-div {
    @apply absolute left-0 top-2 z-50 hidden pl-4 pt-4 sm:block
  }
  .dialog-header-modal-button {
    @apply rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 
  }

  .dialog-modal-examples-div {
    @apply h-full w-full overflow-y-auto scrollbar-hide
  }
  .dialog-modal-example-true {
    @apply mx-auto flex flex-row flex-wrap items-start justify-center overflow-auto
  }
  .dialog-modal-example-false {
    @apply flex flex-row items-center justify-center
  }
  .dialog-modal-button-box-div {
    @apply flex-max-width h-full items-center justify-evenly
  }
  .document-icon {
    @apply h-10 w-10 flex-shrink-0
  }
  .loading-component-div {
    @apply flex items-center justify-center align-middle
  }
  .dialog-modal-footer {
    @apply mt-2 flex-max-width items-center justify-center
  }
  .dialog-modal-footer-link {
    @apply flex items-center justify-center text-muted-foreground
  }

  .node-modal-div {
    @apply fixed inset-0 bg-ring bg-opacity-75 transition-opacity
  }
  .node-modal-dialog-arrangement {
    @apply fixed inset-0 z-10 overflow-y-auto
  }
  .node-modal-dialog-div {
    @apply flex h-full items-end justify-center p-4 text-center sm:items-center sm:p-0
  }
  .node-modal-dialog-panel {
    @apply relative flex h-[600px] w-[700px] transform flex-col justify-between overflow-hidden rounded-lg bg-background text-left shadow-xl transition-all sm:my-8
  }
  .node-modal-dialog-panel-div {
    @apply absolute right-0 top-0 z-50 hidden pr-4 pt-4 sm:block
  }
  .node-modal-dialog-button {
    @apply rounded-md text-ring hover:text-accent-foreground
  }
  .node-modal-dialog-icon-div {
    @apply flex-max-width h-full flex-col items-center justify-center
  }
  .node-modal-icon-arrangement {
    @apply z-10 flex-max-width justify-center pb-4 shadow-sm
  }
  .node-modal-icon {
    @apply mt-4 h-10 w-10 rounded p-1
  }
  .node-modal-title-div {
    @apply mt-4 text-center sm:ml-4 sm:text-left
  }
  .node-modal-title {
    @apply text-lg font-medium leading-10 text-foreground
  }
  .node-modal-template-div {
    @apply flex-max-width h-full flex-row items-center justify-center gap-4 bg-input p-4
  }
  .node-modal-template {
    @apply w-full rounded-lg bg-background px-4 shadow sm:p-4
  }
  .node-modal-template-column {
    @apply flex h-full flex-col gap-5
  }
  .node-modal-button-box {
    @apply flex-max-width flex-row-reverse bg-input px-4 pb-3
  }
  .node-modal-button {
    @apply inline-flex w-full justify-center rounded-md border border-transparent bg-status-red px-4 py-2 text-base font-medium text-background shadow-sm hover:bg-ring sm:ml-3 sm:w-auto sm:text-sm
  }
  .node-modal-button:focus {
    @apply focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-1
  }

  .prompt-modal-icon-box {
    @apply mx-auto mt-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-almost-light-blue sm:mx-0 sm:h-10 sm:w-10
  }
  .prompt-modal-icon {
    @apply h-6 w-6 text-almost-medium-blue
  }
  .prompt-modal-txtarea-arrangement {
    @apply flex-max-width h-full flex-row items-center justify-center gap-4 overflow-auto bg-accent p-4
  }
  .prompt-modal-txtarea-box {
    @apply h-full w-full overflow-hidden rounded-lg bg-background px-4 py-5 shadow sm:p-6
  }
  .prompt-modal-txtarea {
    @apply form-input h-full w-full rounded-lg border-ring
  }

  .txtarea-modal-arrangement {
    @apply flex h-full w-full flex-row items-center justify-center gap-4 bg-input p-4
  }
  .txtarea-modal-box {
    @apply w-full overflow-hidden rounded-lg bg-background px-4 py-5 shadow sm:p-6
  }
  .txtarea-modal-input {
    @apply form-input h-full w-full
  }

  .api-modal-tabs {
    @apply w-full h-full overflow-hidden text-center bg-muted rounded-md border
  }
  .api-modal-tablist-div {
    @apply flex items-center justify-between px-2
  }
  .api-modal-tabs-content {
    @apply overflow-hidden w-full h-full px-4 pb-4 -mt-1
  }
  .api-modal-accordion-display {
    @apply flex w-full h-full mt-2
  }
  .api-modal-table-arrangement {
    @apply flex flex-col gap-5 h-fit
  }

  .icons-parameters-comp{
    @apply ml-3 h-6 w-6
  }

  .form-modal-lock-true {
    @apply bg-input text-primary
  }
  .form-modal-no-input {
    @apply bg-input text-center text-primary dark:bg-gray-700 dark:text-gray-300
  }
  .form-modal-lock-false {
    @apply bg-white text-primary
  }
  .code-highlight{
    @apply block px-3 py-2 w-full max-h-[64vh] text-sm outline-0 border-0 break-all overflow-y-hidden
  }

  .code-nohighlight{
    @apply block px-3 py-2 w-full max-h-[70vh] text-sm outline-0 border-0 break-all overflow-y-hidden
  }
  .form-modal-lockchat {
    @apply form-input focus:ring-ring focus:border-ring block w-full rounded-md border-border p-4 pr-16 custom-scroll sm:text-sm
  }
  .form-modal-send-icon-position {
    @apply absolute bottom-2 right-4
  }
  .form-modal-send-button {
    @apply rounded-md p-2 px-1 transition-all duration-300
  }
  .form-modal-lock-icon {
    @apply ml-1 mr-1 h-5 w-5 animate-pulse
  }
  .form-modal-send-icon {
    @apply mr-2 h-5 w-5 rotate-[44deg]
  }
  .form-modal-play-icon {
    @apply h-5 w-5 mx-1
  }
  .form-modal-chat-position {
    @apply flex-max-width px-2 py-6 pl-4 pr-9
  }
  .form-modal-chatbot-icon {
    @apply mb-3 ml-3 mr-6 mt-1
  }
  .form-modal-chat-image {
    @apply flex flex-col items-center gap-1
  }
  .form-modal-chat-img-box {
    @apply relative flex h-8 w-8 items-center justify-center overflow-hidden rounded-md p-5 text-2xl
  }
  .form-modal-chat-bot-icon {
    @apply form-modal-chat-img-box bg-chat-bot-icon
  }
  .form-modal-chat-user-icon {
    @apply form-modal-chat-img-box bg-chat-user-icon
  }
  .form-modal-chat-icon-img {
    @apply absolute scale-[60%]
  }
  .form-modal-chat-text-position {
    @apply flex w-full flex-1 text-start
  }
  .form-modal-chat-text {
    @apply relative flex w-full flex-col text-start text-sm font-normal text-muted-foreground
  }
  .form-modal-chat-icon-div {
    @apply absolute -left-6 -top-3 cursor-pointer
  }
  .form-modal-chat-icon {
    @apply h-4 w-4 animate-bounce
  }
  .form-modal-chat-thought-border {
    @apply rounded-md border border-ring/60
  }
  .form-modal-chat-thought-size {
    @apply inline-block h-full w-[95%]
  }
  .form-modal-chat-thought {
    @apply cursor-pointer overflow-scroll bg-background text-start text-primary scrollbar-hide form-modal-chat-thought-border form-modal-chat-thought-size py-2 px-2
  }
  .form-modal-markdown-span {
    @apply mt-1 animate-pulse cursor-default
  }
  .form-modal-initial-prompt-btn {
    @apply mb-2 flex items-center gap-2 rounded-md border border-border bg-background shadow-sm px-4 py-2 text-sm font-semibold
  }
  .form-modal-iv-box {
    @apply mt-2 flex-max-width h-[80vh]
  }
  .form-modal-iv-size {
    @apply mr-6 flex h-full w-2/6 flex-col justify-start overflow-auto scrollbar-hide
  }
  .file-component-arrangement {
    @apply flex items-center py-2
  }
  .file-component-variable {
    @apply -ml-px mr-1 h-4 w-4 text-primary
  }
  .file-component-variables-span {
    @apply font-semibold text-primary
  }
  .file-component-variables-title {
    @apply flex items-center justify-between pt-2
  }
  .file-component-variables-div {
    @apply mr-2.5 flex items-center
  }
  .file-component-variables-title-txt {
    @apply text-sm font-medium text-primary
  }
  .file-component-accordion-div {
    @apply flex items-start gap-3
  }
  .file-component-badge-div {
    @apply flex-max-width items-center justify-between
  }
  .file-component-tab-column {
    @apply flex flex-col gap-2 p-1
  }
  .tab-accordion-badge-div {
    @apply flex flex-1 items-center justify-between py-4 text-sm font-normal text-muted-foreground transition-all
  }
  .eraser-column-arrangement {
    @apply flex-max-width flex-1 flex-col
  }
  .eraser-size {
    @apply relative flex h-full w-full flex-col rounded-md border bg-muted
  }
  .eraser-position {
    @apply absolute right-3 top-3 z-50
  }
  .chat-message-div {
    @apply flex-max-width h-full flex-col items-center overflow-scroll scrollbar-hide
  }
  .chat-alert-box {
    @apply flex-max-width h-full flex-col items-center justify-center text-center align-middle
  }
  .bisheng-chat-span {
    @apply text-lg text-foreground
  }
  .bisheng-chat-desc {
    @apply w-2/4 rounded-md border border-border bg-muted px-6 py-8
  }
  .bisheng-chat-desc-span {
    @apply text-base text-muted-foreground
  }
  .bisheng-chat-input-div {
    @apply flex-max-width flex-col items-center justify-between px-8 pb-6
  }
  .bisheng-chat-input {
    @apply relative w-full rounded-md shadow-sm
  }

  .tooltip-fixed-width{
    @apply max-w-[30vw] max-h-[20vh] overflow-auto
  }
  .langflow-chat-input {
    @apply relative w-full rounded-md shadow-sm;
  }
  
  .ace-editor-arrangement {
    @apply flex-max-width h-full flex-col transition-all
  }
  .ace-editor {
    @apply h-full w-full rounded-lg border-[1px] border-border custom-scroll
  }
  .ace-editor-save-btn {
    @apply flex-max-width h-fit justify-end
  }

  .export-modal-save-api {
    @apply font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70
  }

  .chat-message-highlight {
    @apply px-0.5 rounded-md bg-indigo-100 dark:bg-indigo-900
  }

  .navlink.active{
    @apply bg-[#fff] text-primary shadow-sm dark:bg-[#34353A] dark:text-[#FFFFFF] dark:shadow-sm
  }

  /* new ui */
  .bisheng-label {
    @apply text-sm text-muted-foreground font-medium leading-none
  }

  .bisheng-tip {
    @apply text-xs text-[#D8341E] ml-1
  }

  .bisheng-title {
    @apply text-lg text-gray-950 dark:text-gray-50
  }

  .bisheng-table-footer {
    @apply flex justify-between items-center absolute bottom-0 right-0 h-16 w-full bg-[#fff] dark:bg-[#1b1b1b] [&>.desc]:whitespace-nowrap [&>.desc]:text-sm [&>.desc]:text-muted-foreground [&>.desc]:break-keep
  }
  .bisheng-bg {
    @apply bg-[#f4f5f8] dark:bg-[#111]
  }

  .flex-between-center {
    @apply flex justify-between items-center
  }

  .dragging {
    background-color: var(--tw-bg-muted);
    border-color: var(--tw-border-gray-200);
  }

  .bisheng-node {
    @apply relative flex w-[332px] flex-col justify-center rounded-[20px] border-4 group-hover:shadow-sm
  }
  .bisheng-node-top{
    @apply bg-[#EFF1F5] dark:bg-background rounded-t-[20px] px-3 py-1.5
  }
  .bisheng-node-head{
    overflow: hidden;
    position: relative;
    background: #F9F9FA;
    @apply p-3 border-t border-[#cad0de] dark:bg-background dark:border-gray-700
  }

  .bisheng-flow-handle {
    @apply h-6 w-6 bg-transparent border-none flex justify-center items-center
  }
  .bisheng-flow-handle:hover span{
    @apply size-10/12
  }
  .bisheng-flow-handle span {
    @apply w-2 h-2 bg-primary rounded-full transition-all relative pointer-events-none
  }
  .bisheng-flow-handle span::before {
    content: '';
    @apply absolute bg-[#fff] w-[1px] h-2/3 top-[3px] left-1/2 transform -translate-x-1/2 opacity-0 transition-opacity duration-300;
  }
  
  .bisheng-flow-handle span::after {
    content: '';
    @apply absolute bg-[#fff] h-[1px] w-2/3 left-[3px] top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-300;
  }
  
  /* .bisheng-flow-handle:hover {
    @apply bg-primary;
  } */
  
  .bisheng-flow-handle:hover span::before,
  .bisheng-flow-handle:hover span::after {
    @apply opacity-100 ;
  }
  .textarea-badge{
    @apply border border-[#E8EDF2] rounded-md px-1 bg-[#EFF3F8] text-primary mr-2 mb-1 inline-block
  }
  .textarea-error {
    @apply border border-red-500 rounded-md px-1 bg-red-100 text-primary
  }
  .bisheng-richtext{
    @apply text-[#111]
  }
  .bisheng-richtext.placeholder{
    @apply text-gray-400
  }
  /* .textarea-badge::before{
    content: '(x)';
  } */
  /* .bisheng-flow-handle:hover::after{
    content: '';
    @apply size-full rounded-full absolute bg-primary top-0 animate-ping pointer-events-none
  } */
}
