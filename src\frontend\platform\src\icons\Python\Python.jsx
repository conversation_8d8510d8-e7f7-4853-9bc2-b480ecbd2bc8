export const SvgPython = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={1000}
    height={1000}
    viewBox="0 0 750 750"
    {...props}
  >
    <defs>
      <clipPath id="a">
        <path d="M45 42h493v494H45Zm0 0" />
      </clipPath>
      <clipPath id="b">
        <path d="M375.207 42.145c-168.414 0-157.902 73.035-157.902 73.035l.207 75.664h160.703v22.699H153.64S45.906 201.313 45.906 371.262c0 169.906 94.055 163.894 94.055 163.894h56.137v-78.86s-3.028-94.054 92.562-94.054h159.375s89.555 1.453 89.555-86.547V130.188s13.601-88.043-162.383-88.043Zm-88.621 50.87c15.984 0 28.918 12.938 28.918 28.922 0 15.981-12.934 28.918-28.918 28.918-.95 0-1.895-.042-2.836-.136a28.448 28.448 0 0 1-5.563-1.098c-.906-.277-1.8-.594-2.675-.957-.875-.36-1.73-.766-2.567-1.21a29.522 29.522 0 0 1-2.437-1.458 29.114 29.114 0 0 1-6.29-5.703 28.46 28.46 0 0 1-1.69-2.281 27.818 27.818 0 0 1-1.458-2.434 29.596 29.596 0 0 1-1.215-2.566 28.942 28.942 0 0 1-2.055-8.238c-.09-.946-.136-1.891-.132-2.837 0-15.984 12.934-28.921 28.918-28.921Zm0 0" />
      </clipPath>
      <clipPath id="d">
        <path d="M217 209h493v494H217Zm0 0" />
      </clipPath>
      <clipPath id="e">
        <path d="M379.977 702.059c168.414 0 157.902-73.036 157.902-73.036l-.207-75.664h-160.7V530.66h224.551s107.758 12.23 107.758-157.7c0-169.925-94.054-163.894-94.054-163.894H559.09v78.84s3.023 94.051-92.563 94.051H307.152s-89.558-1.45-89.558 86.55v145.509s-13.598 88.043 162.383 88.043Zm88.62-50.875a28.573 28.573 0 0 1-5.644-.547 29.24 29.24 0 0 1-2.754-.688 28.56 28.56 0 0 1-5.242-2.168 29.024 29.024 0 0 1-4.715-3.148 29.42 29.42 0 0 1-2.105-1.906 29.448 29.448 0 0 1-1.907-2.102 29.882 29.882 0 0 1-1.687-2.281 28.516 28.516 0 0 1-2.672-5.004 27.907 27.907 0 0 1-.953-2.676 27.808 27.808 0 0 1-.688-2.754 28.29 28.29 0 0 1-.414-2.808 28.598 28.598 0 0 1-.136-2.836c0-15.961 12.937-28.899 28.918-28.899 15.984 0 28.922 12.914 28.922 28.899 0 16.004-12.938 28.918-28.922 28.918Zm0 0" />
      </clipPath>
      <linearGradient
        id="c"
        x1={16.152}
        x2={40.334}
        y1={15.849}
        y2={40.112}
        gradientTransform="matrix(20.73047 0 0 20.73046 -285.782 -289.543)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0} stopColor="#387EB8" />
        <stop offset={0.125} stopColor="#387EB8" />
        <stop offset={0.141} stopColor="#387EB7" />
        <stop offset={0.156} stopColor="#387DB7" />
        <stop offset={0.172} stopColor="#387DB6" />
        <stop offset={0.188} stopColor="#387CB5" />
        <stop offset={0.203} stopColor="#387CB4" />
        <stop offset={0.219} stopColor="#387BB3" />
        <stop offset={0.234} stopColor="#387BB2" />
        <stop offset={0.25} stopColor="#387AB1" />
        <stop offset={0.266} stopColor="#387AB1" />
        <stop offset={0.281} stopColor="#3879B0" />
        <stop offset={0.297} stopColor="#3879AF" />
        <stop offset={0.313} stopColor="#3878AE" />
        <stop offset={0.328} stopColor="#3778AD" />
        <stop offset={0.344} stopColor="#3777AC" />
        <stop offset={0.359} stopColor="#3777AB" />
        <stop offset={0.375} stopColor="#3776AA" />
        <stop offset={0.391} stopColor="#3776AA" />
        <stop offset={0.406} stopColor="#3775A9" />
        <stop offset={0.422} stopColor="#3775A8" />
        <stop offset={0.438} stopColor="#3774A7" />
        <stop offset={0.453} stopColor="#3774A6" />
        <stop offset={0.469} stopColor="#3773A5" />
        <stop offset={0.484} stopColor="#3773A4" />
        <stop offset={0.498} stopColor="#3772A4" />
        <stop offset={0.5} stopColor="#3772A3" />
        <stop offset={0.502} stopColor="#3772A3" />
        <stop offset={0.516} stopColor="#3771A2" />
        <stop offset={0.531} stopColor="#3771A2" />
        <stop offset={0.547} stopColor="#3771A1" />
        <stop offset={0.563} stopColor="#3770A0" />
        <stop offset={0.578} stopColor="#37709F" />
        <stop offset={0.594} stopColor="#376F9E" />
        <stop offset={0.609} stopColor="#376F9E" />
        <stop offset={0.625} stopColor="#376E9D" />
        <stop offset={0.641} stopColor="#376E9C" />
        <stop offset={0.656} stopColor="#366D9B" />
        <stop offset={0.672} stopColor="#366D9A" />
        <stop offset={0.688} stopColor="#366C99" />
        <stop offset={0.703} stopColor="#366C98" />
        <stop offset={0.719} stopColor="#366B97" />
        <stop offset={0.734} stopColor="#366B97" />
        <stop offset={0.75} stopColor="#366A96" />
        <stop offset={0.766} stopColor="#366A95" />
        <stop offset={0.781} stopColor="#366994" />
        <stop offset={0.813} stopColor="#366994" />
        <stop offset={0.875} stopColor="#366994" />
        <stop offset={1} stopColor="#366994" />
      </linearGradient>
      <linearGradient
        id="f"
        x1={23.548}
        x2={48.282}
        y1={23.842}
        y2={47.538}
        gradientTransform="matrix(20.73047 0 0 20.73046 -285.782 -289.543)"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset={0} stopColor="#FFE052" />
        <stop offset={0.125} stopColor="#FFE052" />
        <stop offset={0.188} stopColor="#FFE052" />
        <stop offset={0.219} stopColor="#FFE052" />
        <stop offset={0.234} stopColor="#FFDF51" />
        <stop offset={0.25} stopColor="#FFDF51" />
        <stop offset={0.266} stopColor="#FFDE50" />
        <stop offset={0.281} stopColor="#FFDD4F" />
        <stop offset={0.297} stopColor="#FFDD4E" />
        <stop offset={0.313} stopColor="#FFDC4E" />
        <stop offset={0.328} stopColor="#FFDB4D" />
        <stop offset={0.344} stopColor="#FFDB4C" />
        <stop offset={0.359} stopColor="#FFDA4B" />
        <stop offset={0.375} stopColor="#FFD94B" />
        <stop offset={0.391} stopColor="#FFD94A" />
        <stop offset={0.406} stopColor="#FFD849" />
        <stop offset={0.422} stopColor="#FFD748" />
        <stop offset={0.438} stopColor="#FFD748" />
        <stop offset={0.453} stopColor="#FFD647" />
        <stop offset={0.469} stopColor="#FFD546" />
        <stop offset={0.484} stopColor="#FFD545" />
        <stop offset={0.486} stopColor="#FFD445" />
        <stop offset={0.5} stopColor="#FFD444" />
        <stop offset={0.514} stopColor="#FFD444" />
        <stop offset={0.516} stopColor="#FFD343" />
        <stop offset={0.531} stopColor="#FFD343" />
        <stop offset={0.547} stopColor="#FFD242" />
        <stop offset={0.563} stopColor="#FFD242" />
        <stop offset={0.578} stopColor="#FFD141" />
        <stop offset={0.594} stopColor="#FFD040" />
        <stop offset={0.609} stopColor="#FFD03F" />
        <stop offset={0.625} stopColor="#FFCF3F" />
        <stop offset={0.641} stopColor="#FFCE3E" />
        <stop offset={0.656} stopColor="#FFCE3D" />
        <stop offset={0.672} stopColor="#FFCD3C" />
        <stop offset={0.688} stopColor="#FFCC3C" />
        <stop offset={0.703} stopColor="#FFCC3B" />
        <stop offset={0.719} stopColor="#FFCB3A" />
        <stop offset={0.734} stopColor="#FFCA39" />
        <stop offset={0.75} stopColor="#FFCA39" />
        <stop offset={0.766} stopColor="#FFC938" />
        <stop offset={0.781} stopColor="#FFC837" />
        <stop offset={0.797} stopColor="#FFC836" />
        <stop offset={0.813} stopColor="#FFC736" />
        <stop offset={0.828} stopColor="#FFC635" />
        <stop offset={0.844} stopColor="#FFC634" />
        <stop offset={0.859} stopColor="#FFC533" />
        <stop offset={0.875} stopColor="#FFC433" />
        <stop offset={0.891} stopColor="#FFC432" />
        <stop offset={0.906} stopColor="#FFC331" />
        <stop offset={0.938} stopColor="#FFC331" />
        <stop offset={1} stopColor="#FFC331" />
      </linearGradient>
    </defs>
    <path fill="#fff" d="M-75-75h900v900H-75z" />
    <path fill="#fff" d="M-75-75h900v900H-75z" />
    <g clipPath="url(#a)">
      <g clipPath="url(#b)">
        <path fill="url(#c)" d="M45.906 42.145v499.023h505.285V42.145Zm0 0" />
      </g>
    </g>
    <g clipPath="url(#d)">
      <g clipPath="url(#e)">
        <path fill="url(#f)" d="M203.996 203.035V702.06h505.285V203.035Zm0 0" />
      </g>
    </g>
  </svg>
);
export default SvgPython;
