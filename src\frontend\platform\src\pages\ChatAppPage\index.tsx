import { TitleLogo } from "@/components/bs-comp/cardComponent";
import { useMessageStore } from "@/components/bs-comp/chatComponent/messageStore";
import LoadMore from "@/components/bs-comp/loadMore";
import { AssistantIcon, SkillIcon } from "@/components/bs-icons";
import { PlusBoxIcon, PlusBoxIconDark } from "@/components/bs-icons/plusBox";
import { bsConfirm } from "@/components/bs-ui/alertDialog/useConfirm";
import { message } from "@/components/bs-ui/toast/use-toast";
import { formatDate, formatStrTime } from "@/util/utils";
import { Trash2 } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { deleteChatApi, getChatsApi } from "../../controllers/API";
import { captureAndAlertRequestErrorHoc } from "../../controllers/request";
import { useDebounce } from "../../util/hook";
import { generateUUID } from "../../utils";
import HomePage from "./components/ChatHome";
import ChatPanne from "./components/ChatPanne";

const ChatItem = ({ chat, chatId, location, handleSelectChat, handleDeleteChat }) => {

    return (
        <div
            className={`group item w-full rounded-lg mb-4 bg-white relative hover:bg-[#f9f9fc] border cursor-pointer dark:hover:bg-[#34353A] ${location
                ? 'bg-[#f9f9fc] dark:bg-[#212122]'
                : (chatId === chat.chat_id
                    ? 'bg-[#EDEFF6] dark:bg-[#34353A]'
                    : 'bg-[#f9f9fc] dark:bg-[#212122]')}`}
            onClick={() => handleSelectChat(chat)}
        >
            {/* <div className="flex">
                <div style={{ width: '24px', height: '24px' }} className="inline-block w-[24px] h-[24px] mr-[8px]">
                    <img src={__APP_ENV__.BASE_URL + '/appLogo.svg'} className="w-[24px] h-[24px]" alt="appLogo" />
                </div>
                <p className="flex-1 truncate text-sm font-bold leading-6">{chat.flow_name}</p>
            </div> */}
            {/* <span className="block text-xs text-gray-600 dark:text-[#8D8D8E] mt-3 break-words truncate">
                {chat.latest_message?.message || ''}
            </span> */}
            <div className="flex justify-between p-4">
                <div className={`text-xs truncate ${chatId === chat.chat_id ? 'font-bold' : 'text-gray-400'}`}>{chat.chat_id}</div>
                <Trash2
                    size={14}
                    className="shrink-0 hidden group-hover:block group-hover:text-red-500"
                    onClick={(e) => handleDeleteChat(e, chat.chat_id)}
                />
            </div>
        </div>
    );
};


export default function SkillChatPage() {
    const { t } = useTranslation()
    const [selectChat, setSelelctChat] = useState<any>({
        id: '', chatId: '', type: ''
    })
    const [options, setOptions] = useState([])

    // 对话列表
    const { chatList, chatId, chatsRef, setChatId, addChat, deleteChat, onScrollLoad } = useChatList()

    const [location, setLocation] = useState(true)
    const onAddChat = () => {
        // console.log('zzz-x', options)
        const target = options.find(item => item.id === groupChecked)
        if (target) {
            setLocation(false)
            handlerSelectFlow(target)
        } else {
            setLocation(true)
        }
    }
    // select flow(新建会话)
    const handlerSelectFlow = async (card) => {
        if (!card) return message({ title: t('prompt'), variant: 'warning', description: t('chat.pleaseSelectAnApp') })
        // 会话ID
        const _chatId = generateUUID(32)
        // add list
        setGroupChecked(card.id)
        addChat({
            "logo": card.logo || '',
            "flow_name": card.name,
            "flow_description": card.description,
            "flow_id": card.id,
            "chat_id": _chatId,
            "create_time": "-",
            "update_time": Date.now(),
            "flow_type": card.flow_type
        })
        setSelelctChat({ id: card.id, chatId: _chatId, type: card.flow_type })
        setChatId(_chatId)
        setLocation(false)
    }

    // select chat
    const handleSelectChat = useDebounce(async (chat) => {
        setLocation(false)
        if (chat.chat_id === chatId) return
        setSelelctChat({ id: chat.flow_id, chatId: chat.chat_id, type: chat.flow_type })
        setChatId(chat.chat_id)
    }, 100, false)

    // del
    const handleDeleteChat = (e, id) => {
        e.stopPropagation();
        bsConfirm({
            desc: t('chat.confirmDeleteChat'),
            onOk(next) {
                deleteChat(id);
                if (selectChat.chatId === id) {
                    setLocation(true)
                }
                setSelelctChat({ id: '', chatId: '', type: '' })
                next()
            }
        })
    }

    const chatMap = useMemo(() => {
        // 先对 chatList 按 create_time 降序排序
        const list = [...chatList].sort((a, b) => {
            return new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
        })
        return list.reduce((acc, chat) => {
            const key = chat.flow_id
            if (!acc[key]) {
                acc[key] = { ...chat, children: [] }
            }
            if (chat.flow_description) {
                acc[key].flow_description = chat.flow_description
            }
            acc[key].children.push(chat)
            return acc
        }, {})
    }, [chatList])

    // 提取所有 flow_id 对应的数组组成新列表
    const chatGroups: any[] = useMemo(() => {
        return Object.values(chatMap).flat()
    }, [chatMap])

    const [groupChecked, setGroupChecked] = useState<string>()
    const chatChecked = useMemo(() => chatMap[groupChecked], [chatMap, groupChecked])
    const onCheckGroup = (data: any) => {
        console.log('zzz-chatMap', chatMap, options)
        setGroupChecked(data)
    }

    return <div className="flex h-full">
        <div className="shrink-0 flex flex-col h-full w-[220px] relative border-r">
            <div className="flex w-full bg-[#EDF4FF] round-tl-md bs-chat-bg shadow-md bg-background-main-content z-10">
                <div className="flex justify-around items-center w-full h-[48px] px-10 py-2 mx-auto text-center text-sm bg-background-main-content bg-white-100 dark:hover:bg-gray-800 relative z-10">
                    历史会话
                </div>
            </div>
            <div className="flex w-auto m-4 bg-[#EDF4FF] round-tl-md bs-chat-bg border bg-background-main-content z-10">
                <div onClick={() => setLocation(true)} id="newchat" className="flex justify-around items-center w-full h-[48px] px-10 py-2 mx-auto text-center text-sm cursor-pointer bg-background-main-content bg-white-100 hover:shadow-md dark:hover:bg-gray-800 relative z-10">
                    <span>选择会话</span>
                </div>
            </div>
            <div className="border border-dashed" />
            <div className="flex-auto scroll overflow-y-scroll no-scrollbar p-4 px-[12px] bg-[rgb(248, 251, 255, .5)">
                {
                    chatGroups.map((chatGroup) => (
                        <div
                            key={chatGroup.flow_id}
                            className="w-full rounded-lg bg-white p-4 mb-4 relative hover:bg-[#f9f9fc] border shadow-md cursor-pointer dark:hover:bg-[#34353A]"
                            onClick={() => onCheckGroup(chatGroup.flow_id)}>
                            <div className="flex items-center justify-center ">
                                <div style={{ width: '24px', height: '24px' }} className="shrink-0 w-[24px] h-[24px] mr-[8px]">
                                    <img src={__APP_ENV__.BASE_URL + '/assistLogo.svg'} className="w-[24px] h-[24px]" alt="appLogo" />
                                </div>
                                <div className="flex-auto">
                                    <p className="flex-1 text-sm font-bold leading-6">{chatGroup.flow_name}</p>
                                </div>
                            </div>
                            {chatGroup.flow_description && <div className="mt-3 text-gray-400 text-xs truncate">{chatGroup.flow_description}</div>}
                        </div>
                    ))
                }
            </div>
        </div>
        {chatChecked && (
            <div className="shrink-0 flex flex-col h-full w-[220px] relative border-r">
                <div className="w-full bg-[#EDF4FF] round-tl-md bs-chat-bg shadow-md bg-background-main-content z-10 ">
                    <div className="flex justify-around items-center w-full h-[48px] px-10 py-2 mx-auto text-center text-sm bg-background-main-content bg-white-100 dark:hover:bg-gray-800 relative z-10">
                        {chatChecked?.flow_name}
                    </div>
                </div>
                <div className="flex w-auto m-4 bg-[#EDF4FF] round-tl-md bs-chat-bg border bg-background-main-content z-10">
                    <div onClick={() => onAddChat()} className="flex justify-around items-center w-full h-[48px] px-10 py-2 mx-auto text-center text-sm cursor-pointer bg-background-main-content bg-white-100 hover:shadow-md dark:hover:bg-gray-800 relative z-10">
                        <PlusBoxIcon className="dark:hidden rounded-tl-lg"></PlusBoxIcon>
                        {t('chat.newChat')}
                    </div>
                </div>
                <div className="border border-dashed" />
                <div className="flex-auto scroll overflow-y-scroll no-scrollbar p-4 px-[12px] bg-[rgb(248, 251, 255, .5)">
                    {
                        chatChecked?.children.map((chat) => (
                            <ChatItem
                                key={chat.chat_id}
                                chat={chat}
                                chatId={chatId}
                                location={location}
                                handleSelectChat={handleSelectChat}
                                handleDeleteChat={handleDeleteChat}
                            />
                        ))
                    }
                </div>
            </div>
        )}
        {/* chat */}
        {
            location
                ? <HomePage onSelect={handlerSelectFlow} onOptionsChange={setOptions}></HomePage>
                : <ChatPanne appendHistory data={selectChat}></ChatPanne>
        }
    </div>
};

/**
 * 本地对话列表
 */
const useChatList = () => {
    const [id, setId] = useState('')
    const [chatList, setChatList] = useState([])
    const chatsRef = useRef(null)
    const { chatId, messages } = useMessageStore()

    useEffect(() => {
        if (messages.length > 0) {
            let latest: any = messages[messages.length - 1]
            // 有分割线取上一条
            if (latest.category === 'divider') latest = messages[messages.length - 2] || {}
            setChatList(chats => chats.map(chat => (chat.chat_id === chatId)
                ? {
                    ...chat,
                    update_time: latest.update_time || formatDate(new Date(), 'yyyy-MM-ddTHH:mm:ss'),
                    latest_message: {
                        ...chat.latest_message,
                        message: (latest.thought || latest.message[latest.chatKey] || latest.message).substring(0, 40)
                    }
                }
                : chat)
            )
        }
    }, [messages, chatId])

    // const pageRef = useRef(0)
    const onScrollLoad = async () => {
        // pageRef.current++
        // const res = await getChatsApi(pageRef.current)
        // setChatList((chats => [...chats, ...res]))
    }

    const getAllChats = async () => {
        let list = []
        let page = 1
        try {
            let res = [] as any[]
            do {
                res = await getChatsApi(page)
                list = [...list, ...res]
                page++
            } while (res.length > 0)
        } catch (error) {
            // 
        }
        setChatList(list)
    }
    useEffect(() => {
        getAllChats()
    }, [])

    return {
        chatList,
        chatId: id,
        chatsRef,
        setChatId: setId,
        addChat: (chat) => {
            const newList = [chat, ...chatList]
            // localStorage.setItem(ITEM_KEY, JSON.stringify(newList))
            setChatList(newList)
            setId(chat.chat_id)
            // setTimeout(() => {
            //     chatsRef.current.scrollTop = 1
            // }, 0);
        },
        deleteChat: (id: string) => {
            // api
            captureAndAlertRequestErrorHoc(deleteChatApi(id).then(res => {
                setChatList(oldList => oldList.filter(item => item.chat_id !== id))
            }))
        },
        onScrollLoad
    }
}

